@extends('components.layouts.admin')

@section('title', 'Quiz Management - ' . $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Quiz Management</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">Quiz Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Header with Actions -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="quiz-status-indicator mr-3">
                            <div class="status-badge {{ $quiz->is_active ? 'status-active' : 'status-inactive' }}">
                                <i class="fas fa-{{ $quiz->is_active ? 'check-circle' : 'pause-circle' }} mr-1"></i>
                                {{ $quiz->is_active ? 'Active' : 'Inactive' }}
                            </div>
                        </div>
                        <div>
                            <h4 class="mb-1">{{ $quiz->title }}</h4>
                            <p class="text-muted mb-0">{{ $quiz->questions->count() }} questions • {{ $quiz->getTotalPossibleScore() }} max points</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-outline-primary">
                            <i class="fas fa-file-alt mr-1"></i>Landing Page
                        </a>
                        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-1"></i>Back
                        </a>
                    </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            <!-- Navigation Tabs -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="questions-tab" data-toggle="tab" href="#questions" role="tab">
                                <i class="fas fa-question-circle mr-2"></i>Questions
                                <span class="badge badge-primary ml-1">{{ $quiz->questions->count() }}</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="settings-tab" data-toggle="tab" href="#settings" role="tab">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="scoring-tab" data-toggle="tab" href="#scoring" role="tab">
                                <i class="fas fa-gauge-high mr-2"></i>Scoring
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="preview-tab" data-toggle="tab" href="#preview" role="tab">
                                <i class="fas fa-eye mr-2"></i>Preview
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Questions Tab -->
                        <div class="tab-pane fade show active" id="questions" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0">Quiz Questions</h5>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addQuestionModal">
                                    <i class="fas fa-plus mr-2"></i>Add Question
                                </button>
                            </div>

                            <!-- Questions List -->
                @if($quiz->questions->count() > 0)
                    <div id="questions-list">
                        @foreach($quiz->questions as $question)
                            <div class="question-item border rounded p-3 mb-3" data-question-id="{{ $question->id }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-secondary mr-2">{{ $question->sort_order }}</span>
                                            @if($question->category)
                                                <span class="badge mr-2" style="background-color: {{ $question->category->color }}; color: white;">
                                                    {{ $question->category->name }}
                                                </span>
                                            @endif
                                            <span class="badge bg-info mr-2">{{ ucfirst(str_replace('_', ' ', $question->type)) }}</span>
                                            <span class="badge bg-primary">{{ $question->points }} pts</span>
                                            @if($question->is_required)
                                                <span class="badge bg-warning ml-2">Required</span>
                                            @endif
                                        </div>
                                        <h6 class="mb-2">{{ $question->question }}</h6>

                                        @if($question->hasOptions())
                                            <div class="options-preview">
                                                <small class="text-muted">Options:</small>
                                                <ul class="list-unstyled ml-3 mb-0">
                                                    @foreach($question->getFormattedOptions() as $key => $option)
                                                        <li class="small">
                                                            <i class="fas fa-circle fa-xs mr-1"></i>
                                                            {{ $option['text'] }} ({{ $option['points'] }} pts)
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="btn-group ml-3">
                                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                                data-toggle="modal"
                                                data-target="#editQuestionModal{{ $question->id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                data-toggle="modal"
                                                data-target="#deleteQuestionModal{{ $question->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary drag-handle">
                                            <i class="fas fa-grip-vertical"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Edit Question Modal -->
                            <div class="modal fade" id="editQuestionModal{{ $question->id }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Edit Question</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                                        </div>
                                        <form method="POST" action="{{ route('admin.quizzes.questions.update', [$landingPage, $question]) }}">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-body">
                                                <!-- Question form fields would go here -->
                                                <div class="mb-3">
                                                    <label class="form-label">Question</label>
                                                    <input type="text" class="form-control" name="question" value="{{ $question->question }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Category</label>
                                                    <select class="form-select" name="category_id">
                                                        <option value="">No Category</option>
                                                        @foreach($categories as $category)
                                                            <option value="{{ $category->id }}" {{ $question->category_id == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">Type</label>
                                                            <select class="form-select" name="type" required>
                                                                @foreach(\App\Models\QuizQuestion::getTypes() as $value => $label)
                                                                    <option value="{{ $value }}" {{ $question->type === $value ? 'selected' : '' }}>
                                                                        {{ $label }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">Points</label>
                                                            <input type="number" class="form-control" name="points" value="{{ $question->points }}" min="1" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="edit-options-container-{{ $question->id }}" style="{{ $question->hasOptions() ? '' : 'display: none;' }}">
                                                    <label class="form-label">Options</label>
                                                    <div id="edit-options-list-{{ $question->id }}">
                                                        @if($question->hasOptions())
                                                            @foreach($question->getFormattedOptions() as $key => $option)
                                                                <div class="input-group mb-2">
                                                                    <input type="text" class="form-control" name="options[{{ $key }}][text]" value="{{ $option['text'] }}" placeholder="Option text" required>
                                                                    <input type="number" class="form-control" name="options[{{ $key }}][points]" value="{{ $option['points'] }}" placeholder="Points" min="0" style="max-width: 100px;" required>
                                                                    <button type="button" class="btn btn-outline-danger remove-option">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary add-option" data-question-id="{{ $question->id }}">
                                                        <i class="fas fa-plus mr-1"></i>Add Option
                                                    </button>
                                                </div>

                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="checkbox" name="is_required" value="1" {{ $question->is_required ? 'checked' : '' }}>
                                                    <label class="form-check-label">Required</label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-primary">Update Question</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Question Modal -->
                            <div class="modal fade" id="deleteQuestionModal{{ $question->id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Delete Question</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete this question?</p>
                                            <p class="text-muted small">{{ $question->question }}</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <form method="POST" action="{{ route('admin.quizzes.questions.destroy', [$landingPage, $question]) }}" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">No questions yet</h6>
                                    <p class="text-muted">Add questions to create your assessment quiz.</p>
                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addQuestionModal">
                                        <i class="fas fa-plus mr-2"></i>Add First Question
                                    </button>
                                </div>
                            @endif
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="settings" role="tabpanel">
                            <h5 class="mb-4">Quiz Settings</h5>
                            <form method="POST" action="{{ route('admin.quizzes.update', $landingPage) }}">
                                @csrf
                                @method('PUT')

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Quiz Title</label>
                                            <input type="text" class="form-control" id="title" name="title" value="{{ old('title', $quiz->title) }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mt-4">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $quiz->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">Active</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $quiz->description) }}</textarea>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i>Update Quiz
                                </button>
                            </form>
                        </div>

                        <!-- Scoring Tab -->
                        <div class="tab-pane fade" id="scoring" role="tabpanel">
                            <h5 class="mb-4">Score Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-gauge-high fa-3x text-warning mb-3"></i>
                                            <h6>Gauge Display</h6>
                                            <p class="text-muted small">Configure how scores are displayed with gauges and colors</p>
                                            <a href="{{ route('admin.quizzes.score-config', $landingPage) }}" class="btn btn-warning">
                                                <i class="fas fa-cog mr-1"></i>Configure Scoring
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-folder fa-3x text-success mb-3"></i>
                                            <h6>Categories</h6>
                                            <p class="text-muted small">Organize questions into categories for better structure</p>
                                            <a href="{{ route('admin.quiz-categories.index', $landingPage) }}" class="btn btn-success">
                                                <i class="fas fa-folder mr-1"></i>Manage Categories
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Tab -->
                        <div class="tab-pane fade" id="preview" role="tabpanel">
                            <h5 class="mb-4">Quiz Preview & Actions</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-external-link-alt fa-2x text-info mb-3"></i>
                                            <h6>Preview Quiz</h6>
                                            <p class="text-muted small">See how your quiz looks to users</p>
                                            <a href="{{ route('landing-pages.quiz', $landingPage) }}" target="_blank" class="btn btn-info">
                                                <i class="fas fa-eye mr-1"></i>Open Preview
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-primary mb-3"></i>
                                            <h6>View Responses</h6>
                                            <p class="text-muted small">See all quiz submissions and scores</p>
                                            <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-primary">
                                                <i class="fas fa-chart-bar mr-1"></i>View Results
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-file-alt fa-2x text-secondary mb-3"></i>
                                            <h6>Landing Page</h6>
                                            <p class="text-muted small">Return to the main landing page settings</p>
                                            <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-secondary">
                                                <i class="fas fa-file-alt mr-1"></i>Landing Page
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Question</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.quizzes.questions.store', $landingPage) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="question" class="form-label">Question *</label>
                        <input type="text" class="form-control" id="question" name="question" required>
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">No Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                        <div class="form-text">
                            <a href="{{ route('admin.quiz-categories.index', $landingPage) }}" target="_blank">
                                <i class="fas fa-plus mr-1"></i>Manage Categories
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Question Type *</label>
                                <select class="form-select" id="type" name="type" required>
                                    @foreach(\App\Models\QuizQuestion::getTypes() as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="points" class="form-label">Points *</label>
                                <input type="number" class="form-control" id="points" name="points" value="10" min="1" required>
                            </div>
                        </div>
                    </div>

                    <div id="options-container" style="display: none;">
                        <label class="form-label">Options</label>
                        <div id="options-list">
                            <!-- Options will be added dynamically -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="add-option">
                            <i class="fas fa-plus mr-1"></i>Add Option
                        </button>
                    </div>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="is_required" name="is_required" value="1" checked>
                        <label class="form-check-label" for="is_required">Required</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Question</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Quiz management script loaded');

    // Add Question Modal
    const typeSelect = document.getElementById('type');
    const optionsContainer = document.getElementById('options-container');
    const optionsList = document.getElementById('options-list');
    const addOptionBtn = document.getElementById('add-option');

    console.log('Elements found:', {
        typeSelect: !!typeSelect,
        optionsContainer: !!optionsContainer,
        optionsList: !!optionsList,
        addOptionBtn: !!addOptionBtn
    });

    let optionIndex = 0;

    function toggleOptionsContainer() {
        const selectedType = typeSelect.value;
        if (selectedType === 'multiple_choice' || selectedType === 'single_choice') {
            optionsContainer.style.display = 'block';
            if (optionsList.children.length === 0) {
                addOption();
                addOption();
            }
        } else {
            optionsContainer.style.display = 'none';
            optionsList.innerHTML = '';
            optionIndex = 0;
        }
    }

    function addOption() {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'input-group mb-2';
        optionDiv.innerHTML = `
            <input type="text" class="form-control" name="options[${optionIndex}][text]" placeholder="Option text" required>
            <input type="number" class="form-control" name="options[${optionIndex}][points]" placeholder="Points" value="1" min="0" style="max-width: 100px;" required>
            <button type="button" class="btn btn-outline-danger remove-option">
                <i class="fas fa-times"></i>
            </button>
        `;

        optionsList.appendChild(optionDiv);
        optionIndex++;

        // Add remove functionality
        optionDiv.querySelector('.remove-option').addEventListener('click', function() {
            optionDiv.remove();
        });
    }

    typeSelect.addEventListener('change', toggleOptionsContainer);
    addOptionBtn.addEventListener('click', addOption);

    // Initialize
    toggleOptionsContainer();

    // Edit Question Modals
    document.querySelectorAll('[id^="editQuestionModal"]').forEach(function(modal) {
        const questionId = modal.id.replace('editQuestionModal', '');
        const editTypeSelect = modal.querySelector('select[name="type"]');
        const editOptionsContainer = document.getElementById('edit-options-container-' + questionId);
        const editOptionsList = document.getElementById('edit-options-list-' + questionId);
        const editAddOptionBtn = modal.querySelector('.add-option');

        let editOptionIndex = editOptionsList ? editOptionsList.children.length : 0;

        function toggleEditOptionsContainer() {
            const selectedType = editTypeSelect.value;
            if (selectedType === 'multiple_choice' || selectedType === 'single_choice') {
                editOptionsContainer.style.display = 'block';
                if (editOptionsList.children.length === 0) {
                    addEditOption();
                    addEditOption();
                }
            } else {
                editOptionsContainer.style.display = 'none';
                editOptionsList.innerHTML = '';
                editOptionIndex = 0;
            }
        }

        function addEditOption() {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'input-group mb-2';
            optionDiv.innerHTML = `
                <input type="text" class="form-control" name="options[${editOptionIndex}][text]" placeholder="Option text" required>
                <input type="number" class="form-control" name="options[${editOptionIndex}][points]" placeholder="Points" value="1" min="0" style="max-width: 100px;" required>
                <button type="button" class="btn btn-outline-danger remove-option">
                    <i class="fas fa-times"></i>
                </button>
            `;

            editOptionsList.appendChild(optionDiv);
            editOptionIndex++;

            // Add remove functionality
            optionDiv.querySelector('.remove-option').addEventListener('click', function() {
                optionDiv.remove();
            });
        }

        editTypeSelect.addEventListener('change', toggleEditOptionsContainer);
        editAddOptionBtn.addEventListener('click', addEditOption);

        // Handle existing remove buttons
        editOptionsList.querySelectorAll('.remove-option').forEach(function(btn) {
            btn.addEventListener('click', function() {
                btn.closest('.input-group').remove();
            });
        });
    });

    // Initialize sortable for questions
    if (typeof Sortable !== 'undefined') {
        const questionsList = document.getElementById('questions-list');
        if (questionsList) {
            new Sortable(questionsList, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    const questionIds = Array.from(questionsList.children).map(item =>
                        item.getAttribute('data-question-id')
                    );

                    fetch('{{ route("admin.quizzes.questions.reorder", $landingPage) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            questions: questionIds
                        })
                    });
                }
            });
        }
    }
});
</script>

<style>
/* Custom styles for the redesigned quiz management page */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.nav-tabs-custom {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.nav-tabs-custom .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs-custom .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-bottom-color: #007bff;
}

.question-item {
    transition: all 0.2s ease;
    background-color: #fff;
}

.question-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.drag-handle {
    cursor: move;
}

.drag-handle:hover {
    color: #007bff;
}

.badge {
    font-size: 0.75rem;
}

.options-preview {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.card.bg-light:hover {
    background-color: #e9ecef !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.quiz-status-indicator {
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    .nav-tabs-custom .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
}
</style>
@endsection
