@extends('components.layouts.admin')

@section('title', 'Score Configuration - ' . $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Score Configuration</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.show', $landingPage) }}">{{ $landingPage->title }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.quizzes.show', $landingPage) }}">Quiz</a></li>
                        <li class="breadcrumb-item active">Score Configuration</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Header with Preview -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="mr-3">
                            <div class="score-preview-badge">
                                <i class="fas fa-gauge-high mr-1"></i>
                                Score Configuration
                            </div>
                        </div>
                        <div>
                            <h4 class="mb-1">{{ $landingPage->title }}</h4>
                            <p class="text-muted mb-0">Configure how quiz scores are displayed and interpreted</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-1"></i>Back to Quiz
                        </a>
                        <a href="{{ route('landing-pages.quiz', $landingPage) }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-eye mr-1"></i>Preview
                        </a>
                    </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            @endif

            <!-- Main Configuration Form -->
            <div class="row">
                <div class="col-12">
                    <form action="{{ route('admin.quizzes.score-config.update', $landingPage) }}" method="POST" id="score-config-form">
                        @csrf
                        @method('PUT')

                        <!-- Tabbed Interface -->
                        <div class="card">
                    <div class="card-header p-0">
                        <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="gauge-tab" data-toggle="tab" href="#gauge" role="tab">
                                    <i class="fas fa-gauge-high mr-2"></i>Gauge Display
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="colors-tab" data-toggle="tab" href="#colors" role="tab">
                                    <i class="fas fa-palette mr-2"></i>Color Ranges
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="results-tab" data-toggle="tab" href="#results" role="tab">
                                    <i class="fas fa-comment-alt mr-2"></i>Result Messages
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="preview-tab" data-toggle="tab" href="#preview" role="tab">
                                    <i class="fas fa-eye mr-2"></i>Live Preview
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Gauge Display Tab -->
                            <div class="tab-pane fade show active" id="gauge" role="tabpanel">
                                <h5 class="mb-4">Gauge Display Settings</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-cog mr-2 text-primary"></i>Basic Settings
                                                </h6>

                                                <div class="form-group">
                                                    <label for="gauge_config_min_value">Minimum Value</label>
                                                    <input type="number" class="form-control" id="gauge_config_min_value"
                                                           name="gauge_config[min_value]" value="{{ $gaugeConfig['min_value'] }}" required>
                                                    <small class="form-text text-muted">The lowest possible score value</small>
                                                </div>

                                                <div class="form-group">
                                                    <label for="gauge_config_max_value">Maximum Value</label>
                                                    <input type="number" class="form-control" id="gauge_config_max_value"
                                                           name="gauge_config[max_value]" value="{{ $gaugeConfig['max_value'] }}" required>
                                                    <small class="form-text text-muted">The highest possible score value</small>
                                                </div>

                                                <div class="form-group">
                                                    <label for="gauge_config_gauge_type">Gauge Type</label>
                                                    <select class="form-control" id="gauge_config_gauge_type" name="gauge_config[gauge_type]" required>
                                                        <option value="semicircle" {{ $gaugeConfig['gauge_type'] === 'semicircle' ? 'selected' : '' }}>Semi-circle</option>
                                                        <option value="full_circle" {{ $gaugeConfig['gauge_type'] === 'full_circle' ? 'selected' : '' }}>Full Circle</option>
                                                        <option value="linear" {{ $gaugeConfig['gauge_type'] === 'linear' ? 'selected' : '' }}>Linear</option>
                                                    </select>
                                                    <small class="form-text text-muted">Choose how the gauge is displayed</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-eye mr-2 text-success"></i>Display Options
                                                </h6>

                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" id="gauge_config_show_percentage"
                                                           name="gauge_config[show_percentage]" {{ $gaugeConfig['show_percentage'] ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gauge_config_show_percentage">
                                                        <strong>Show Percentage Symbol</strong>
                                                        <br><small class="text-muted">Display "%" after the score number</small>
                                                    </label>
                                                </div>

                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" id="gauge_config_show_labels"
                                                           name="gauge_config[show_labels]" {{ $gaugeConfig['show_labels'] ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gauge_config_show_labels">
                                                        <strong>Show Range Labels</strong>
                                                        <br><small class="text-muted">Display labels like "Good", "Excellent" below the score</small>
                                                    </label>
                                                </div>

                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" id="gauge_config_animate"
                                                           name="gauge_config[animate]" {{ $gaugeConfig['animate'] ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gauge_config_animate">
                                                        <strong>Enable Animation</strong>
                                                        <br><small class="text-muted">Animate the gauge fill when the page loads</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Ranges Tab -->
                            <div class="tab-pane fade" id="colors" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h5 class="mb-0">Color Ranges Configuration</h5>
                                    <button type="button" class="btn btn-success" id="add-color-range">
                                        <i class="fas fa-plus mr-2"></i>Add Color Range
                                    </button>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>Color ranges</strong> determine what color the gauge displays for different score values.
                                    Make sure your ranges don't overlap and cover all possible scores.
                                </div>

                                <div id="color-ranges">
                                    @foreach(($gaugeConfig['color_ranges'] ?? []) as $index => $range)
                                        <div class="color-range-item card mb-3">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <span class="color-preview" style="background-color: {{ $range['color'] ?? '#48bb78' }}; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-right: 10px;"></span>
                                                    Range {{ $index + 1 }}: {{ $range['label'] ?? 'Range ' . ($index + 1) }}
                                                </h6>
                                                <button type="button" class="btn btn-danger btn-sm remove-color-range">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <label>Minimum Score</label>
                                                        <input type="number" class="form-control"
                                                               name="gauge_config[color_ranges][{{ $index }}][min]"
                                                               value="{{ $range['min'] ?? 0 }}" required>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label>Maximum Score</label>
                                                        <input type="number" class="form-control"
                                                               name="gauge_config[color_ranges][{{ $index }}][max]"
                                                               value="{{ $range['max'] ?? 100 }}" required>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label>Color</label>
                                                        <input type="color" class="form-control color-input"
                                                               name="gauge_config[color_ranges][{{ $index }}][color]"
                                                               value="{{ $range['color'] ?? '#48bb78' }}" required>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label>Label</label>
                                                        <input type="text" class="form-control"
                                                               name="gauge_config[color_ranges][{{ $index }}][label]"
                                                               value="{{ $range['label'] ?? 'Range ' . ($index + 1) }}" required
                                                               placeholder="e.g., Excellent">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Result Messages Tab -->
                            <div class="tab-pane fade" id="results" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h5 class="mb-0">Result Messages Configuration</h5>
                                    <button type="button" class="btn btn-success" id="add-result-text">
                                        <i class="fas fa-plus mr-2"></i>Add Result Range
                                    </button>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>Result messages</strong> are shown to users based on their quiz score.
                                    Create personalized messages that provide value and encourage consultation booking.
                                </div>

                                <div id="result-texts">
                                    @foreach(($resultTexts['ranges'] ?? []) as $index => $range)
                                        <div class="result-text-item card mb-4">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-comment-alt mr-2 text-primary"></i>
                                                    {{ $range['title'] ?? 'Range ' . ($index + 1) }} ({{ $range['min'] ?? 0 }}-{{ $range['max'] ?? 100 }} points)
                                                </h6>
                                                <button type="button" class="btn btn-danger btn-sm remove-result-text">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div class="row mb-3">
                                                    <div class="col-md-2">
                                                        <label>Min Score</label>
                                                        <input type="number" class="form-control"
                                                               name="result_texts[ranges][{{ $index }}][min]"
                                                               value="{{ $range['min'] }}" required>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label>Max Score</label>
                                                        <input type="number" class="form-control"
                                                               name="result_texts[ranges][{{ $index }}][max]"
                                                               value="{{ $range['max'] }}" required>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <label>Result Title</label>
                                                        <input type="text" class="form-control"
                                                               name="result_texts[ranges][{{ $index }}][title]"
                                                               value="{{ $range['title'] }}" required
                                                               placeholder="e.g., Excellent Automation Readiness">
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label>Description</label>
                                                    <textarea class="form-control" rows="3"
                                                              name="result_texts[ranges][{{ $index }}][description]" required
                                                              placeholder="Describe what this score means and what the user can expect...">{{ $range['description'] }}</textarea>
                                                </div>

                                                <div class="form-group">
                                                    <label>Consultation Recommendations</label>
                                                    <textarea class="form-control" rows="4"
                                                              name="result_texts[ranges][{{ $index }}][recommendations_text]"
                                                              placeholder="Enter what will be covered in the consultation (one item per line)">{{ isset($range['recommendations']) ? implode("\n", $range['recommendations']) : '' }}</textarea>
                                                    <small class="form-text text-muted">Each line will appear as a bullet point with a checkmark icon</small>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Live Preview Tab -->
                            <div class="tab-pane fade" id="preview" role="tabpanel">
                                <h5 class="mb-4">Live Preview</h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-eye mr-2"></i>Score Display Preview
                                                </h6>
                                            </div>
                                            <div class="card-body text-center">
                                                <div class="preview-score-display">
                                                    <div class="preview-gauge" id="preview-gauge">
                                                        <!-- Gauge preview will be rendered here -->
                                                        <div class="gauge-placeholder">
                                                            <i class="fas fa-gauge-high fa-3x text-muted mb-3"></i>
                                                            <p class="text-muted">Configure settings to see preview</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mt-3">
                                                    <label for="preview-score">Test Score:</label>
                                                    <input type="range" class="form-range" id="preview-score" min="0" max="100" value="75">
                                                    <div class="d-flex justify-content-between">
                                                        <small>0</small>
                                                        <span id="preview-score-value">75</span>
                                                        <small>100</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-comment-alt mr-2"></i>Result Message Preview
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="preview-result-text">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-comment-dots fa-2x mb-3"></i>
                                                        <p>Adjust the test score to see different result messages</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-warning mt-4">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    <strong>Note:</strong> This is a simplified preview. The actual display on your landing page may look slightly different based on your theme and styling.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save mr-2"></i>Save Configuration
                                </button>
                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times mr-1"></i>Cancel
                                </a>
                            </div>
                            <div class="text-muted">
                                <small><i class="fas fa-info-circle mr-1"></i>Changes will be applied immediately to your quiz</small>
                            </div>
                        </div>
                    </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let colorRangeIndex = {{ count($gaugeConfig['color_ranges'] ?? []) }};
    let resultTextIndex = {{ count($resultTexts['ranges'] ?? []) }};

    // Add color range functionality
    document.getElementById('add-color-range').addEventListener('click', function() {
        const container = document.getElementById('color-ranges');
        const newRange = document.createElement('div');
        newRange.className = 'color-range-item card mb-3';
        newRange.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <span class="color-preview" style="background-color: #48bb78; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-right: 10px;"></span>
                    Range ${colorRangeIndex + 1}: New Range
                </h6>
                <button type="button" class="btn btn-danger btn-sm remove-color-range">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label>Minimum Score</label>
                        <input type="number" class="form-control"
                               name="gauge_config[color_ranges][${colorRangeIndex}][min]"
                               value="0" required>
                    </div>
                    <div class="col-md-3">
                        <label>Maximum Score</label>
                        <input type="number" class="form-control"
                               name="gauge_config[color_ranges][${colorRangeIndex}][max]"
                               value="100" required>
                    </div>
                    <div class="col-md-3">
                        <label>Color</label>
                        <input type="color" class="form-control color-input"
                               name="gauge_config[color_ranges][${colorRangeIndex}][color]"
                               value="#48bb78" required>
                    </div>
                    <div class="col-md-3">
                        <label>Label</label>
                        <input type="text" class="form-control"
                               name="gauge_config[color_ranges][${colorRangeIndex}][label]"
                               value="Range ${colorRangeIndex + 1}" required
                               placeholder="e.g., Excellent">
                    </div>
                </div>
            </div>
        `;
        container.appendChild(newRange);
        colorRangeIndex++;
        updateColorPreviews();
    });

    // Remove color range functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-color-range') || e.target.closest('.remove-color-range')) {
            const item = e.target.closest('.color-range-item');
            if (document.querySelectorAll('.color-range-item').length > 1) {
                item.remove();
            } else {
                alert('At least one color range is required.');
            }
        }
    });

    // Add result text functionality
    document.getElementById('add-result-text').addEventListener('click', function() {
        const container = document.getElementById('result-texts');
        const newResultText = document.createElement('div');
        newResultText.className = 'result-text-item card mb-4';
        newResultText.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-comment-alt mr-2 text-primary"></i>
                    New Range (0-100 points)
                </h6>
                <button type="button" class="btn btn-danger btn-sm remove-result-text">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-2">
                        <label>Min Score</label>
                        <input type="number" class="form-control"
                               name="result_texts[ranges][${resultTextIndex}][min]"
                               value="0" required>
                    </div>
                    <div class="col-md-2">
                        <label>Max Score</label>
                        <input type="number" class="form-control"
                               name="result_texts[ranges][${resultTextIndex}][max]"
                               value="100" required>
                    </div>
                    <div class="col-md-8">
                        <label>Result Title</label>
                        <input type="text" class="form-control"
                               name="result_texts[ranges][${resultTextIndex}][title]"
                               value="New Range" required
                               placeholder="e.g., Excellent Automation Readiness">
                    </div>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea class="form-control" rows="3"
                              name="result_texts[ranges][${resultTextIndex}][description]" required
                              placeholder="Describe what this score means and what the user can expect..."></textarea>
                </div>

                <div class="form-group">
                    <label>Consultation Recommendations</label>
                    <textarea class="form-control" rows="4"
                              name="result_texts[ranges][${resultTextIndex}][recommendations_text]"
                              placeholder="Enter what will be covered in the consultation (one item per line)"></textarea>
                    <small class="form-text text-muted">Each line will appear as a bullet point with a checkmark icon</small>
                </div>
            </div>
        `;
        container.appendChild(newResultText);
        resultTextIndex++;
    });

    // Remove result text functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-result-text') || e.target.closest('.remove-result-text')) {
            const item = e.target.closest('.result-text-item');
            if (document.querySelectorAll('.result-text-item').length > 1) {
                item.remove();
            } else {
                alert('At least one result text range is required.');
            }
        }
    });

    // Color input change handler
    function updateColorPreviews() {
        document.querySelectorAll('.color-input').forEach(function(input) {
            input.addEventListener('change', function() {
                const preview = this.closest('.card').querySelector('.color-preview');
                if (preview) {
                    preview.style.backgroundColor = this.value;
                }
                const header = this.closest('.card').querySelector('.card-header h6');
                if (header) {
                    const labelInput = this.closest('.card-body').querySelector('input[name*="[label]"]');
                    if (labelInput) {
                        const rangeText = header.textContent.split(':')[0];
                        header.innerHTML = `<span class="color-preview" style="background-color: ${this.value}; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-right: 10px;"></span>${rangeText}: ${labelInput.value}`;
                    }
                }
            });
        });
    }

    // Initialize color previews
    updateColorPreviews();

    // Preview functionality
    const previewScore = document.getElementById('preview-score');
    const previewScoreValue = document.getElementById('preview-score-value');

    if (previewScore && previewScoreValue) {
        previewScore.addEventListener('input', function() {
            previewScoreValue.textContent = this.value;
            updatePreview(parseInt(this.value));
        });
    }

    function updatePreview(score) {
        // Update gauge preview (simplified)
        const gaugePreview = document.getElementById('preview-gauge');
        if (gaugePreview) {
            gaugePreview.innerHTML = `
                <div class="preview-gauge-display">
                    <div class="gauge-circle">
                        <span class="score-number">${score}</span>
                        <span class="score-label">Score</span>
                    </div>
                </div>
            `;
        }

        // Update result text preview
        const resultPreview = document.getElementById('preview-result-text');
        if (resultPreview) {
            const resultRanges = @json($resultTexts['ranges'] ?? []);
            let matchedRange = null;

            for (let range of resultRanges) {
                if (score >= range.min && score <= range.max) {
                    matchedRange = range;
                    break;
                }
            }

            if (matchedRange) {
                let recommendationsHtml = '';
                if (matchedRange.recommendations && matchedRange.recommendations.length > 0) {
                    recommendationsHtml = `
                        <h6 class="mt-3">What to Expect:</h6>
                        <ul class="list-unstyled">
                            ${matchedRange.recommendations.map(rec => `<li><i class="fas fa-check-circle text-success mr-2"></i>${rec}</li>`).join('')}
                        </ul>
                    `;
                }

                resultPreview.innerHTML = `
                    <h5 class="text-primary">${matchedRange.title}</h5>
                    <p class="text-muted">${matchedRange.description}</p>
                    ${recommendationsHtml}
                `;
            } else {
                resultPreview.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>No result text configured for score ${score}</p>
                    </div>
                `;
            }
        }
    }

    // Initialize preview
    updatePreview(75);

    // Form submission processing for recommendations
    document.querySelector('form').addEventListener('submit', function(e) {
        // Convert recommendations text to arrays
        const recommendationTextareas = document.querySelectorAll('textarea[name*="recommendations_text"]');
        recommendationTextareas.forEach(function(textarea, index) {
            const lines = textarea.value.split('\n').filter(line => line.trim() !== '');
            lines.forEach(function(line, lineIndex) {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = `result_texts[ranges][${index}][recommendations][${lineIndex}]`;
                hiddenInput.value = line.trim();
                textarea.parentNode.appendChild(hiddenInput);
            });
        });
    });
});
</script>

<style>
/* Custom styles for the redesigned score configuration page */
.score-preview-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.nav-tabs-custom {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.nav-tabs-custom .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs-custom .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-bottom-color: #007bff;
}

.color-range-item:hover,
.result-text-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

.color-preview {
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.color-input {
    height: 40px;
    padding: 2px;
    border-radius: 0.375rem;
}

.preview-gauge-display {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
}

.gauge-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.score-number {
    font-size: 1.5rem;
    line-height: 1;
}

.score-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.25rem;
}

.gauge-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

.card.bg-light:hover {
    background-color: #e9ecef !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Prevent overflow and ensure proper width constraints */
.content-wrapper {
    overflow-x: hidden;
}

#score-config-form {
    max-width: 100%;
    overflow-x: hidden;
}

.card {
    max-width: 100%;
    overflow-x: hidden;
}

.tab-content {
    max-width: 100%;
    overflow-x: hidden;
}

.row {
    margin-left: 0;
    margin-right: 0;
}

.col-md-3, .col-md-6, .col-md-8, .col-md-2 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Ensure form elements don't overflow */
.form-control, .btn {
    max-width: 100%;
    box-sizing: border-box;
}

/* Fix button group wrapping */
.btn-group {
    flex-wrap: wrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-tabs-custom .nav-link {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .score-preview-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .col-md-3, .col-md-6, .col-md-8, .col-md-2 {
        margin-bottom: 1rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }
}
</style>
@endsection
