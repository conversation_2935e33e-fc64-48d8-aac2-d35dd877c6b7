<?php

use App\Http\Controllers\ConsultationController;
use App\Http\Controllers\SitemapController;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

// Serve sitemap.xml directly from the public directory
Route::get('/sitemap.xml', function() {
    $path = public_path('sitemap.xml');
    if (!file_exists($path)) {
        abort(404, 'Sitemap not found');
    }
    return response(file_get_contents($path), 200, [
        'Content-Type' => 'application/xml',
        'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
        'Pragma' => 'no-cache',
        'Expires' => '0',
    ]);
})->name('sitemap');

// Removed unused sitemap routes as files were cleaned up

Route::get('/', function () {
    return view('home');
})->name('home');

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('terms-of-service');
})->name('terms-of-service');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    // Dashboard route removed - using admin dashboard instead

    Route::get('settings/profile', App\Livewire\Settings\AdminProfile::class)->name('settings.profile');
    Route::get('settings/password', App\Livewire\Settings\AdminPassword::class)->name('settings.password');
    Route::get('settings/appearance', App\Livewire\Settings\AdminAppearance::class)->name('settings.appearance');
});

// API route for consultation form
Route::post('/api/consultations', [ConsultationController::class, 'store'])
    ->name('web.consultations.store');

// Landing Page Routes
Route::prefix('landing')->name('landing-pages.')->group(function () {
    Route::get('{landingPage}', [App\Http\Controllers\LandingPageController::class, 'show'])
        ->name('show');
    Route::get('{landingPage}/quiz', [App\Http\Controllers\LandingPageController::class, 'quiz'])
        ->name('quiz');
    Route::post('{landingPage}/quiz', [App\Http\Controllers\LandingPageController::class, 'submitQuiz'])
        ->name('quiz.submit');
    Route::get('{landingPage}/schedule', [App\Http\Controllers\LandingPageController::class, 'schedule'])
        ->name('schedule');
    Route::post('{landingPage}/schedule', [App\Http\Controllers\LandingPageController::class, 'submitSchedule'])
        ->name('schedule.submit');
});

// Debug route to check authentication status
Route::get('/auth-check', function () {
    return [
        'authenticated' => auth()->check(),
        'user' => auth()->check() ? [
            'id' => auth()->id(),
            'name' => auth()->user()->name,
            'email' => auth()->user()->email,
            'is_admin' => auth()->user()->is_admin,
        ] : null,
        'session' => [
            'has_session' => session()->isStarted(),
            'session_id' => session()->getId(),
        ],
    ];
});

// Test route for admin middleware
Route::get('/admin-test', function () {
    return [
        'success' => true,
        'message' => 'You have successfully accessed the admin test route',
        'user' => [
            'id' => auth()->id(),
            'name' => auth()->user()->name,
            'email' => auth()->user()->email,
            'is_admin' => auth()->user()->is_admin,
        ],
    ];
})->middleware(['auth', 'admin']);

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', [App\Http\Controllers\Admin\AdminController::class, 'index'])->name('dashboard');

    // Consultation Management
    Route::resource('consultations', App\Http\Controllers\Admin\ConsultationAdminController::class);
    Route::patch('consultations/{consultation}/update-status', [App\Http\Controllers\Admin\ConsultationAdminController::class, 'updateStatus'])
        ->name('consultations.update-status');

    // Consultation Status Management
    Route::resource('consultation-statuses', App\Http\Controllers\Admin\ConsultationStatusAdminController::class);
    Route::patch('consultation-statuses/{consultationStatus}/set-default', [App\Http\Controllers\Admin\ConsultationStatusAdminController::class, 'setDefault'])
        ->name('consultation-statuses.set-default');

    // User Management
    Route::resource('users', App\Http\Controllers\Admin\UserAdminController::class);
    Route::patch('users/{user}/toggle-admin', [App\Http\Controllers\Admin\UserAdminController::class, 'toggleAdmin'])
        ->name('users.toggle-admin');

    // Landing Page Management
    Route::resource('landing-pages', App\Http\Controllers\Admin\LandingPageAdminController::class);

    // Lead Management
    Route::resource('leads', App\Http\Controllers\Admin\LeadAdminController::class);
    Route::get('leads/export', [App\Http\Controllers\Admin\LeadAdminController::class, 'export'])->name('leads.export');

    // Quiz Management
    Route::get('landing-pages/{landingPage}/quiz', [App\Http\Controllers\Admin\QuizAdminController::class, 'show'])->name('quizzes.show');
    Route::put('landing-pages/{landingPage}/quiz', [App\Http\Controllers\Admin\QuizAdminController::class, 'update'])->name('quizzes.update');
    Route::post('landing-pages/{landingPage}/quiz/questions', [App\Http\Controllers\Admin\QuizAdminController::class, 'storeQuestion'])->name('quizzes.questions.store');
    Route::put('landing-pages/{landingPage}/quiz/questions/{question}', [App\Http\Controllers\Admin\QuizAdminController::class, 'updateQuestion'])->name('quizzes.questions.update');
    Route::delete('landing-pages/{landingPage}/quiz/questions/{question}', [App\Http\Controllers\Admin\QuizAdminController::class, 'destroyQuestion'])->name('quizzes.questions.destroy');
    Route::post('landing-pages/{landingPage}/quiz/questions/reorder', [App\Http\Controllers\Admin\QuizAdminController::class, 'reorderQuestions'])->name('quizzes.questions.reorder');

    // Quiz Score Configuration
    Route::get('landing-pages/{landingPage}/quiz/score-config', [App\Http\Controllers\Admin\QuizAdminController::class, 'scoreConfig'])->name('quizzes.score-config');
    Route::put('landing-pages/{landingPage}/quiz/score-config', [App\Http\Controllers\Admin\QuizAdminController::class, 'updateScoreConfig'])->name('quizzes.score-config.update');

    // Quiz Categories Management
    Route::get('landing-pages/{landingPage}/quiz/categories', [App\Http\Controllers\Admin\QuizCategoryController::class, 'index'])->name('quiz-categories.index');
    Route::post('landing-pages/{landingPage}/quiz/categories', [App\Http\Controllers\Admin\QuizCategoryController::class, 'store'])->name('quiz-categories.store');
    Route::put('landing-pages/{landingPage}/quiz/categories/{category}', [App\Http\Controllers\Admin\QuizCategoryController::class, 'update'])->name('quiz-categories.update');
    Route::delete('landing-pages/{landingPage}/quiz/categories/{category}', [App\Http\Controllers\Admin\QuizCategoryController::class, 'destroy'])->name('quiz-categories.destroy');
    Route::post('landing-pages/{landingPage}/quiz/categories/reorder', [App\Http\Controllers\Admin\QuizCategoryController::class, 'reorder'])->name('quiz-categories.reorder');

    // Site Settings
    Route::get('settings', [App\Http\Controllers\Admin\SettingsAdminController::class, 'index'])->name('settings.index');
    Route::get('settings/social-media', [App\Http\Controllers\Admin\SettingsAdminController::class, 'socialMedia'])->name('settings.social-media');
    Route::put('settings/social-media', [App\Http\Controllers\Admin\SettingsAdminController::class, 'updateSocialMedia'])->name('settings.social-media.update');

    // Include API management routes
    require __DIR__.'/admin_api.php';
});

require __DIR__.'/auth.php';
